import React from "react";
import CRUD from "../../../Components/CRUD";

interface IStockDetailsProps {
}

const StockDetails: React.FC<IStockDetailsProps> = () => {
    return <CRUD
        key={crypto.randomUUID()}
        crudApiPath={"/crud/stock_details"}
        columns={
            [
                {
                    type: "select",
                    field: "stockItemUuid",
                    headerName: "Stock Item",
                    dataGridWidth: 250,
                    editorGridSize: 3,
                    apiPath: "/crud/stock_items",
                    displayMember: "particular",
                    valueMember: "uuid",
                    showFilter: true
                },
                {
                    type: "string",
                    field: 'location',
                    headerName: 'Location',
                    dataGridWidth: 200,
                    editorGridSize: 2,
                    additionalTextFieldProps: {
                        inputProps: {
                            maxLength: 50,
                        }
                    }
                },
                {
                    type: "number",
                    field: "currentStock",
                    headerName: "Current Stock",
                    dataGridWidth: 150,
                    editorGridSize: 1.5
                },
                {
                    type: "number",
                    field: "minimumStock",
                    headerName: "Minimum Stock",
                    dataGridWidth: 150,
                    editorGridSize: 1.5
                },
                {
                    type: "number",
                    field: "maximumStock",
                    headerName: "Maximum Stock",
                    dataGridWidth: 150,
                    editorGridSize: 1.5
                },
                {
                    type: "number",
                    field: "reorderLevel",
                    headerName: "Reorder Level",
                    dataGridWidth: 150,
                    editorGridSize: 1.5
                },
                {
                    type: "number",
                    field: "averageCost",
                    headerName: "Average Cost",
                    dataGridWidth: 150,
                    editorGridSize: 1.5
                },
                {
                    type: "number",
                    field: "lastPurchaseCost",
                    headerName: "Last Purchase Cost",
                    dataGridWidth: 180,
                    editorGridSize: 1.5
                },
                {
                    type: "string",
                    field: 'supplierCode',
                    headerName: 'Supplier Code',
                    dataGridWidth: 150,
                    editorGridSize: 2,
                    additionalTextFieldProps: {
                        inputProps: {
                            maxLength: 20,
                        }
                    }
                },
                {
                    type: "string",
                    field: 'batchNumber',
                    headerName: 'Batch Number',
                    dataGridWidth: 150,
                    editorGridSize: 2,
                    additionalTextFieldProps: {
                        inputProps: {
                            maxLength: 30,
                        }
                    }
                },
                {
                    type: "string",
                    field: 'expiryDate',
                    headerName: 'Expiry Date',
                    dataGridWidth: 150,
                    editorGridSize: 2,
                    additionalTextFieldProps: {
                        type: "date"
                    }
                },
                {
                    type: "boolean",
                    field: "isActive",
                    headerName: "Active",
                    dataGridWidth: 100,
                    editorGridSize: 1,
                    defaultValue: true
                }
            ]
        }
    />;
};

export default StockDetails;