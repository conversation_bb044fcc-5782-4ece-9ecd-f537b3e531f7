import React from "react";
import CRUD from "../../../Components/CRUD";

interface IStockItemsProps {
}

const StockItems: React.FC<IStockItemsProps> = () => {
    return <CRUD
        key={crypto.randomUUID()}
        crudApiPath={"/crud/stock_items"}
        columns={
            [
                {
                    type: "string",
                    field: 'particular',
                    headerName: 'Particular',
                    dataGridWidth: 250,
                    editorGridSize: 3,
                    additionalTextFieldProps: {
                        inputProps: {
                            maxLength: 30,
                        }
                    }
                },
                {
                    type: "string",
                    field: 'barCode',
                    headerName: 'Bar Code',
                    dataGridWidth: 250,
                    editorGridSize: 2,
                    additionalTextFieldProps: {
                        inputProps: {
                            maxLength: 15,
                        }
                    }
                },
                {
                    type: "string",
                    field: 'hsn',
                    headerName: 'HSN',
                    dataGridWidth: 250,
                    editorGridSize: 1.5,
                    additionalTextFieldProps: {
                        inputProps: {
                            maxLength: 8
                        }
                    }
                },
                {
                    type: "select",
                    field: "taxRate",
                    selectApiIgnorePathSuffix: true,
                    headerName: "TaxRate",
                    dataGridWidth: 250,
                    editorGridSize: 1,
                    apiPath: "/general/tax-rates",
                    displayMember: "taxRate",
                    valueMember: "taxRate",
                    selectApiPreProcessor: (i:number) => {
                        return {
                            taxRate: i,
                            active: true
                        };
                    },
                    defaultValue:null
                },
                {
                    type: "number",
                    field: "cashDiscountRate",
                    headerName: "Cash Discount Rate",
                    dataGridWidth: 250,
                    editorGridSize: 1.5
                },
                {
                    type: "number",
                    field: "defaultQty",
                    headerName: "Default Qty",
                    dataGridWidth: 250,
                    editorGridSize: 1.5
                },
                {
                    type: "number",
                    field: "boxQty",
                    headerName: "Box Qty",
                    dataGridWidth: 250,
                    editorGridSize: 1.5
                },
                {
                    type: "select",
                    field: "boxUuid",
                    headerName: "Box ID",
                    dataGridWidth: 250,
                    editorGridSize: 3,
                    apiPath: "/crud/stock_items",
                    displayMember: "particular",
                    valueMember: "uuid",
                    includeNoneOption: true,
                    defaultValue: null,
                    showFilter: true
                }
            ]
        }
    />;
};

export default StockItems;
