export interface IGstLineItem {
    taxable: number;
    cgst: number;
    sgst: number;
    igst: number;
}

export interface IDayBookLines {
    [date: string]: IGstLineItem;
}

export interface IBillLines {
    [date: string]: IGstLineItem;
}

export interface IHsnLines {
    [hsnCode: string]: IGstLineItem;
}

export interface IGstReportSection {
    dayBookLines: IDayBookLines;
    dayBookTotal: IGstLineItem;
    billLines: IBillLines;
    billTotal: IGstLineItem;
    hsnLines: IHsnLines | null;
    hsnTotal: IGstLineItem | null;
    startBillNumber: number | null;
    endBillNumber: number | null;
    deletedBills: number[];
    totalBillCount: number | null;
    deletedBillCount: number | null;
    totalLiveBillCount: number | null;
}

export interface IGstReport {
    [sectionKey: string]: IGstReportSection;
}

export interface IMonthlyGstReportResponse {
    month: number;
    year: number;
    report: IGstReport;
}

export interface IMonthlyGstReportRequest {
    month: number;
    year: number;
}

export interface HsnLineItem {
    hsnCode: string;
    
}
