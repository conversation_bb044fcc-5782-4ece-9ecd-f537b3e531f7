import React, { FC, useCallback, useMemo, useState } from "react";
import {
    Card,
    CardContent,
    Box,
    Typography,
    TextField,
    Button,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Paper,
    Alert,
    CircularProgress,
    Accordion,
    AccordionSummary,
    AccordionDetails,
    Chip, Grid
} from "@mui/material";
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { useFormik } from "formik";
import { getMonthlyGstReportValidationSchema } from "./validation";
import { IMonthlyGstReportResponse, IGstLineItem, IGstReportSection } from "./types";
import {apiCall} from "../../../../Utilities";

const BtlMonthlyGstReportPage: FC = () => {
    const [reportData, setReportData] = useState<IMonthlyGstReportResponse | null>(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const formik = useFormik({
        initialValues: {
            month: new Date().getMonth() + 1,
            year: new Date().getFullYear(),
        },
        validationSchema: getMonthlyGstReportValidationSchema(),
        onSubmit: async (values) => {
            setLoading(true);
            setError(null);
            try {
                const response = await apiCall("/btl/sales-bill/monthly-gst-report", values);
                setReportData(response);
            } catch (err: any) {
                setError(err.response?.data?.message || "Failed to fetch report data");
            } finally {
                setLoading(false);
            }
        },
    });

    const formatCurrency = useCallback((amount: number) => {
        return new Intl.NumberFormat('en-IN', {
            style: 'currency',
            currency: 'INR',
            minimumFractionDigits: 2
        }).format(amount);
    }, []);

    const renderLineItemsTable = useCallback((
        title: string,
        data: { [key: string]: IGstLineItem },
        total: IGstLineItem,
        isDateKey: boolean = true
    ) => {
        const entries = Object.entries(data);

        return (
            <Box sx={{ mb: 3 }}>
                <Typography variant="h6" gutterBottom>
                    {title}
                </Typography>
                <TableContainer component={Paper}>
                    <Table size="small">
                        <TableHead>
                            <TableRow>
                                <TableCell>{isDateKey ? "Date" : "HSN Code"}</TableCell>
                                <TableCell align="right">Taxable Amount</TableCell>
                                <TableCell align="right">CGST</TableCell>
                                <TableCell align="right">SGST</TableCell>
                                <TableCell align="right">IGST</TableCell>
                                <TableCell align="right">Total</TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {entries.map(([key, item]) => (
                                <TableRow key={key}>
                                    <TableCell>{key}</TableCell>
                                    <TableCell align="right">{formatCurrency(item.taxable)}</TableCell>
                                    <TableCell align="right">{formatCurrency(item.cgst)}</TableCell>
                                    <TableCell align="right">{formatCurrency(item.sgst)}</TableCell>
                                    <TableCell align="right">{formatCurrency(item.igst)}</TableCell>
                                    <TableCell align="right">
                                        {formatCurrency(item.taxable + item.cgst + item.sgst + item.igst)}
                                    </TableCell>
                                </TableRow>
                            ))}
                            <TableRow sx={{ backgroundColor: '#f5f5f5', fontWeight: 'bold' }}>
                                <TableCell><strong>Total</strong></TableCell>
                                <TableCell align="right"><strong>{formatCurrency(total.taxable)}</strong></TableCell>
                                <TableCell align="right"><strong>{formatCurrency(total.cgst)}</strong></TableCell>
                                <TableCell align="right"><strong>{formatCurrency(total.sgst)}</strong></TableCell>
                                <TableCell align="right"><strong>{formatCurrency(total.igst)}</strong></TableCell>
                                <TableCell align="right">
                                    <strong>{formatCurrency(total.taxable + total.cgst + total.sgst + total.igst)}</strong>
                                </TableCell>
                            </TableRow>
                        </TableBody>
                    </Table>
                </TableContainer>
            </Box>
        );
    }, [formatCurrency]);

    const renderBillStatistics = useCallback((section: IGstReportSection) => {
        if (!section.startBillNumber || !section.endBillNumber) return null;

        return (
            <Box sx={{ mb: 3 }}>
                <Typography variant="h6" gutterBottom>
                    Bill Statistics
                </Typography>
                <Paper sx={{ p: 2 }}>
                    <Grid container direction="row" spacing={2}>
                        <Grid item>
                            <Chip label={`Start Bill: ${section.startBillNumber}`} color="primary" />
                        </Grid>
                        <Grid item>
                            <Chip label={`End Bill: ${section.endBillNumber}`} color="primary" />
                        </Grid>
                        <Grid item>
                            <Chip label={`Total Bills: ${section.totalBillCount}`} color="secondary" />
                        </Grid>
                        <Grid item>
                            <Chip label={`Live Bills: ${section.totalLiveBillCount}`} color="success" />
                        </Grid>
                        <Grid item>
                            <Chip label={`Deleted Bills: ${section.deletedBillCount}`} color="error" />
                        </Grid>
                    </Grid>
                    {section.deletedBills.length > 0 && (
                        <Box sx={{ mt: 2 }}>
                            <Typography variant="body2" color="text.secondary">
                                Deleted Bill Numbers: {section.deletedBills.join(", ")}
                            </Typography>
                        </Box>
                    )}
                </Paper>
            </Box>
        );
    }, []);

    const renderSectionReport = useCallback((sectionKey: string, section: IGstReportSection) => {
        return (
            <Accordion key={sectionKey} defaultExpanded>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="h5">
                        Section {sectionKey} Report
                    </Typography>
                </AccordionSummary>
                <AccordionDetails>
                    <Box>
                        {renderLineItemsTable("Day Book", section.dayBookLines, section.dayBookTotal)}
                        {renderLineItemsTable("Bills", section.billLines, section.billTotal)}
                        {section.hsnLines && section.hsnTotal &&
                            renderLineItemsTable("HSN Wise", section.hsnLines, section.hsnTotal, false)
                        }
                        {renderBillStatistics(section)}
                    </Box>
                </AccordionDetails>
            </Accordion>
        );
    }, [renderLineItemsTable, renderBillStatistics]);

    const formContent = useMemo(() => {
        return (
            <form onSubmit={formik.handleSubmit}>
                <Grid container direction="column" spacing={3}>
                    {/* Title */}
                    <Grid item xs={12}>
                        <Typography variant="h4" align="center">
                            Monthly GST Report
                        </Typography>
                    </Grid>

                    {/* Month & Year Inputs */}
                    <Grid item xs={12}>
                        <Grid container direction="row" spacing={2}>
                            <Grid item xs={6}>
                                <TextField
                                    fullWidth
                                    type="number"
                                    label="Month"
                                    name="month"
                                    inputProps={{ min: 1, max: 12 }}
                                    value={formik.values.month}
                                    onChange={formik.handleChange}
                                    onBlur={formik.handleBlur}
                                    error={formik.touched.month && Boolean(formik.errors.month)}
                                    helperText={formik.touched.month && formik.errors.month}
                                />
                            </Grid>
                            <Grid item xs={6}>
                                <TextField
                                    fullWidth
                                    type="number"
                                    label="Year"
                                    name="year"
                                    inputProps={{ min: 2000, max: 2100 }}
                                    value={formik.values.year}
                                    onChange={formik.handleChange}
                                    onBlur={formik.handleBlur}
                                    error={formik.touched.year && Boolean(formik.errors.year)}
                                    helperText={formik.touched.year && formik.errors.year}
                                />
                            </Grid>
                        </Grid>
                    </Grid>

                    {/* Submit Button */}
                    <Grid item xs={12} sx={{ textAlign: 'center' }}>
                        <Button
                            type="submit"
                            size="large"
                            disabled={loading}
                            startIcon={loading ? <CircularProgress size={20} /> : null}
                        >
                            {loading ? "Generating Report..." : "Generate Report"}
                        </Button>
                    </Grid>
                </Grid>
            </form>
        );
    }, [formik, loading]);

    return (
        <Box sx={{ p: 3 }}>
            <Card raised>
                <CardContent>
                    {formContent}
                </CardContent>
            </Card>

            {error && (
                <Alert severity="error" sx={{ mt: 3 }}>
                    {error}
                </Alert>
            )}

            {reportData && !loading && (
                <Box sx={{ mt: 3 }}>
                    <Typography variant="h4" gutterBottom>
                        GST Report for {reportData.month}/{reportData.year}
                    </Typography>
                    {Object.entries(reportData.report).map(([sectionKey, section]) =>
                        renderSectionReport(sectionKey, section)
                    )}
                </Box>
            )}
        </Box>
    );
};

export default BtlMonthlyGstReportPage;
